# Spring Security JWT 登录认证 API 测试文档

## 项目概述

已完成Spring Security + JWT的登录认证功能，包含以下核心组件：

### 核心组件
1. **JwtUtil** - JWT工具类，负责token的生成、解析和验证
2. **JwtAuthenticationFilter** - JWT认证过滤器，拦截请求并验证token
3. **CustomUserDetailsService** - 用户详情服务，负责加载用户信息
4. **SecurityConfig** - Spring Security配置类
5. **AuthenticationConfig** - 认证管理器配置
6. **LoginController** - 登录控制器
7. **Result** - 统一响应格式

## API 接口

### 1. 登录接口

**请求地址：** `POST /api/login`

**请求体：**
```json
{
    "username": "admin",
    "password": "123456"
}
```

**成功响应：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiJ9...",
        "tokenType": "Bearer",
        "expiresIn": 86400000,
        "username": "admin"
    }
}
```

**失败响应：**
```json
{
    "code": 401,
    "message": "用户名或密码错误",
    "data": null
}
```

### 2. 受保护的接口示例

**请求地址：** `GET /api/sys/user/current`

**请求头：**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
```

**成功响应：**
```json
{
    "code": 200,
    "message": "当前登录用户",
    "data": "admin"
}
```

## 测试步骤

### 使用 curl 测试

1. **登录获取token：**
```bash
curl -X POST http://localhost:8080/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

2. **使用token访问受保护接口：**
```bash
curl -X GET http://localhost:8080/api/sys/user/current \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 使用 Postman 测试

1. **登录：**
   - Method: POST
   - URL: `http://localhost:8080/api/login`
   - Headers: `Content-Type: application/json`
   - Body (raw JSON):
     ```json
     {
         "username": "admin",
         "password": "123456"
     }
     ```

2. **访问受保护接口：**
   - Method: GET
   - URL: `http://localhost:8080/api/sys/user/current`
   - Headers: `Authorization: Bearer YOUR_TOKEN_HERE`

## 默认测试账号

- **用户名：** admin
- **密码：** 123456

## 注意事项

1. **Token有效期：** 24小时（86400000毫秒）
2. **Token格式：** Bearer Token
3. **密码加密：** 使用BCrypt加密
4. **Session管理：** 无状态（STATELESS）
5. **CSRF保护：** 已禁用（适用于API）

## 项目启动

1. 确保MySQL数据库运行并创建了 `deerling_admin` 数据库
2. 运行 `AdminApplication` 主类
3. 访问 `http://localhost:8080/api/login` 进行测试

## 扩展说明

当前使用硬编码的用户信息进行测试，实际项目中需要：

1. 在 `CustomUserDetailsService` 中集成数据库查询
2. 实现用户注册、密码重置等功能
3. 添加角色权限管理
4. 配置JWT密钥和过期时间到配置文件
