package com.dl.admin.sys.user.controller;

import com.dl.admin.sys.user.entity.DTO.LoginParam;
import com.dl.admin.sys.user.entity.DTO.LoginResponse;
import com.dl.admin.sys.user.service.UserService;
import com.dl.base.common.Result;
import com.dl.base.security.CustomUserDetailsService;
import com.dl.base.security.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class LoginController {

    @Autowired
    private CustomUserDetailsService userDetailsService;

    @Autowired
    private UserService userService;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody LoginParam param) {
        try {
            // 使用Spring Security进行认证
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(param.getUsername(), param.getPassword())
            );

            // 认证成功，获取用户详情
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();

            // 生成JWT token
            String token = jwtUtil.generateToken(userDetails);

            // 创建响应对象
            LoginResponse loginResponse = new LoginResponse(token, 86400000L, userDetails.getUsername());

            return Result.success("登录成功", loginResponse);

        } catch (BadCredentialsException e) {
            return Result.error(401, "用户名或密码错误");
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    @RequestMapping("/login-error")
    public Result<String> loginError() {
        return Result.error("登录失败");
    }
}