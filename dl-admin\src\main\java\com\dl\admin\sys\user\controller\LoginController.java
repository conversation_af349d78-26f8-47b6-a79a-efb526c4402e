package com.dl.admin.sys.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dl.admin.sys.user.entity.DO.User;
import com.dl.admin.sys.user.entity.DTO.LoginParam;
import com.dl.admin.sys.user.service.UserService;
import com.dl.base.security.CustomUserDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class LoginController {

    @Autowired
    private CustomUserDetailsService userDetailsService;
    @Autowired
    private UserService userService;


    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody LoginParam param) {

        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername,param.getUsername());
        userService.getOne(queryWrapper);
       return null;
    }

    @RequestMapping("/login-error")
    public String loginError() {
        return "Login failed!";
    }
}