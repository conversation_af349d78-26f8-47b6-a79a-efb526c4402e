package com.dl.base.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractAuthenticationFilterConfigurer;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public UserDetailsService userDetailsService() {
        return new CustomUserDetailsService();
    }
    // 配置密码编码器
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 已废弃，不再推荐使用
        //http.authorizeRequests()
        //       .anyRequest().authenticated()
        //       .and().formLogin()
        //       .and().httpBasic();
        //return http.build();

        // 推荐使用如下方式
        // authorizeHttpRequests 替代了旧的 authorizeRequests 方法。
        // 它接受一个 Consumer<AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry> 类型的参数，
        // 使用 Lambda 表达式来配置请求的授权规则。
        // anyRequest().authenticated() 表示所有的请求都需要进行身份验证。
        http.authorizeHttpRequests(authorize -> authorize.anyRequest().authenticated())
                // formLogin 方法
                // 用于配置表单登录功能。同样接受一个 Consumer<FormLoginConfigurer<HttpSecurity>> 类型的参数。
                // permitAll() 表示允许所有用户访问表单登录相关的页面，例如登录页面、登录处理接口等。
                // 你还可以根据需求进行更多的配置，如自定义登录页面的 URL、登录成功或失败的处理逻辑等。
                .formLogin(AbstractAuthenticationFilterConfigurer::permitAll)

                // 用于启用 HTTP 基本认证，withDefaults() 表示使用默认的 HTTP 基本认证配置。
                .httpBasic(withDefaults());

        return http.build();
    }



}
