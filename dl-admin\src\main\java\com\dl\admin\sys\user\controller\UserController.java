package com.dl.admin.sys.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.admin.sys.user.entity.DO.User;
import com.dl.admin.sys.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/sys/user")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 查询用户列表
     * @return 用户列表
     */
    @GetMapping("/list")
    public List<User> listUsers() {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        return userService.list(queryWrapper);
    }
    
    /**
     * 分页查询用户列表
     * @param current 当前页码
     * @param size 每页条数
     * @return 分页用户列表
     */
    @GetMapping("/page")
    public Page<User> pageUsers(long current, long size) {
        Page<User> page = new Page<>(current, size);
        return userService.page(page);
    }
}