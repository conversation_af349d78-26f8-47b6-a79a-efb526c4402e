package com.dl.admin.sys.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.admin.sys.user.entity.DO.User;
import com.dl.admin.sys.user.service.UserService;
import com.dl.base.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/sys/user")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 查询用户列表
     * @return 用户列表
     */
    @GetMapping("/list")
    public Result<List<User>> listUsers() {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        List<User> users = userService.list(queryWrapper);
        return Result.success(users);
    }

    /**
     * 分页查询用户列表
     * @param current 当前页码
     * @param size 每页条数
     * @return 分页用户列表
     */
    @GetMapping("/page")
    public Result<Page<User>> pageUsers(long current, long size) {
        Page<User> page = new Page<>(current, size);
        Page<User> result = userService.page(page);
        return Result.success(result);
    }

    /**
     * 获取当前登录用户信息
     * @return 当前用户信息
     */
    @GetMapping("/current")
    public Result<String> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String username = authentication.getName();
            return Result.success("当前登录用户", username);
        }
        return Result.error("未登录");
    }
}