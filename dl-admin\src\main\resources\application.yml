server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: deerling-admin
  datasource:
    url: ***********************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 暂时不使用Druid，先用Spring Boot默认的HikariCP
    type: com.alibaba.druid.pool.DruidDataSource

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl